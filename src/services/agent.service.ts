import { RuntimeContext } from "@mastra/core/runtime-context";
import { mastra } from "../mastra";
import { getMoolyAccount } from "./postgres/chatbot.service";
import {
  validateAgentResponse,
  isRetryableError,
  createFallbackResponse,
  logAgentMetrics,
  calculateRetryDelay,
  extractReplyContent
} from "../utils/agent.utils";
import { supabaseAdmin } from "../config/supabase";
import { getProductImages } from "./postgres/product.service";
import { GoogleGenerativeAIProviderOptions } from "@ai-sdk/google";
import { examplesTemplate } from "../mastra/prompts/gemini-optimized-instructions";
import z from "zod";

/**
 * Interface cho thông tin chatbot từ database
 */
interface ChatbotInfo {
  channel_type: string;
  instruction: string | null;
  type?: 'sale' | 'rag_bot' | 'sale_bot';
}



/**
 * Retry configuration
 */
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 1,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
};

/**
 * Sleep function for retry delays
 */
const sleep = (ms: number): Promise<void> =>
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Tạo phản hồi từ agent với retry logic
 *
 * Hỗ trợ hai chế độ:
 * 1. Sử dụng trực tiếp mastra instance (không có telemetry)
 * 2. Sử dụng MastraClient để kết nối đến server (có telemetry)
 *
 * @param message Nội dung tin nhắn
 * @param threadId ID của thread (cuộc hội thoại)
 * @param resourceId ID của resource (người dùng)
 * @param botId ID của chatbot (từ database)
 * @param tenantId ID của tenant (từ database)
 * @param chatbotInfo Thông tin chi tiết về chatbot (tùy chọn)
 * @param accoutnId Account ID
 * @param conversationId Conversation ID
 * @param contactName Tên khách hàng từ webhook
 * @param retryConfig Cấu hình retry (tùy chọn)
 */
export const generateAgentResponse = async (
  message: any,
  threadId?: string,
  resourceId?: string,
  botId?: string,
  tenantId?: string,
  chatbotInfo?: ChatbotInfo | null,
  accoutnId?: string,
  conversationId?: string,
  contactName?: string,
  retryConfig: RetryConfig = DEFAULT_RETRY_CONFIG
) => {
  // Sử dụng thông tin từ database nếu có, nếu không sử dụng giá trị mặc định
  const botIdToUse = botId || "3a38a49b-6ea7-4942-95bf-fc8e612c195a";
  const tenantIdToUse = tenantId || "4ebbbb49-db73-4420-acac-96aaf1670aef";
  const threadIdToUse = threadId || "new_thread";
  const resourceIdToUse = resourceId || "anonymous";

  // Telemetry metadata
  const telemetryMetadata = {
    userId: resourceIdToUse,
    sessionId: threadIdToUse,
    tags: ["tho-tran-shop", "customer-service"],
  };

  // Thêm thông tin về chatbot vào metadata nếu có
  if (chatbotInfo) {
    telemetryMetadata.tags.push(
      `channel:${chatbotInfo?.channel_type || "unknown"}`
    );
    if (chatbotInfo.instruction) {
      telemetryMetadata.tags.push(`bot:${chatbotInfo.instruction}`);
    }
  }
  let apiToken = "";

  if (accoutnId) {
    try {
      const accountResult = await getMoolyAccount({
        accoutnId: accoutnId.toString(),
      });
      if (
        accountResult.success &&
        accountResult.data &&
        accountResult.data.token
      ) {
        apiToken = accountResult.data.token;
      }
    } catch (error) {
      console.error("Lỗi khi lấy thông tin tài khoản:", error);
    }
  }

  // Khởi tạo Mastra client
  // const client = new MastraClient({
  //   baseUrl: process.env.MASTRA_SERVER_URL || "http://localhost:4111",
  // });

  // Chọn agent dựa trên type từ chatbot configuration
  let agentName = "ecommerceAgent"; // Default agent
  let agentTypeInstruction = `
Bạn là một trợ lý AI chuyên nghiệp cho cửa hàng bán lẻ trực tuyến.
Nhiệm vụ của bạn là hỗ trợ khách hàng tìm kiếm sản phẩm, tư vấn về giá cả, biến thể, xử lý đơn hàng và trả lời các câu hỏi về chính sách cửa hàng.
Hãy thân thiện và ngắn gọn trong các phản hồi của bạn.
Bạn có quyền truy cập vào một bộ công cụ, nhưng chỉ sử dụng chúng khi cần thiết.
Nếu bạn không có đủ thông tin để sử dụng công cụ một cách chính xác, hãy đặt câu hỏi tiếp theo cho người dùng để có được thông tin cần thiết.
Không gọi bất kỳ công cụ nào trừ khi bạn có dữ liệu cần thiết từ người dùng.

Trong mỗi lượt hội thoại, bạn sẽ bắt đầu bằng việc suy nghĩ về phản hồi của mình.
Sau khi hoàn thành, bạn sẽ viết một phản hồi hướng đến người dùng.
Điều quan trọng là phải đặt tất cả các phản hồi hội thoại hướng đến người dùng trong thẻ XML <reply></reply> để dễ dàng phân tích.

Quy tắc quan trọng:
- LUÔN sử dụng công cụ phù hợp để lấy thông tin chính xác từ hệ thống trước khi trả lời về sản phẩm, giá cả, tồn kho hoặc đơn hàng
- KHÔNG bịa đặt thông tin - chỉ sử dụng dữ liệu từ công cụ
- Sử dụng tên khách hàng ${contactName ? contactName : ''} một cách tự nhiên khi có thể
- Khi cần gửi hình ảnh sản phẩm theo product_id, sử dụng công cụ sendImagesTool
- Không được gửi url hình ảnh trực tiếp trong <reply> xml tag.
- Khi cần gửi hình ảnh theo danh sách URL, sử dụng công cụ sendImagesTool
- Nếu phát hiện spam hoặc ngôn từ không phù hợp, sử dụng humanHandoffTool để chuyển cho nhân viên
- Không được chào hỏi lại liên tục, phải tiếp nối cuộc trò chuyện tự nhiên nhất.
  `

  if (chatbotInfo?.type) {
    switch (chatbotInfo.type) {
      case 'sale':
      case 'sale_bot':
        agentName = "ecommerceAgent";
        break;
      case 'rag_bot':
        agentName = "ragAgent";
        agentTypeInstruction = `
Bạn là một trợ lý AI chuyên nghiệp cho dịch vụ tư vấn và hỗ trợ khách hàng.
Nhiệm vụ của bạn là giúp khách hàng tìm hiểu thông tin về các câu hỏi thường gặp, chính sách, dịch vụ và hướng dẫn sử dụng.
Hãy thân thiện và ngắn gọn trong các phản hồi của bạn.
Bạn có quyền truy cập vào một bộ công cụ, nhưng chỉ sử dụng chúng khi cần thiết.
Nếu bạn không có đủ thông tin để sử dụng công cụ một cách chính xác, hãy đặt câu hỏi tiếp theo cho người dùng để có được thông tin cần thiết.
Không gọi bất kỳ công cụ nào trừ khi bạn có dữ liệu cần thiết từ người dùng.

Trong mỗi lượt hội thoại, bạn sẽ bắt đầu bằng việc suy nghĩ về phản hồi của mình.
Sau khi hoàn thành, bạn sẽ viết một phản hồi hướng đến người dùng.
Điều quan trọng là phải đặt tất cả các phản hồi hội thoại hướng đến người dùng trong thẻ XML <reply></reply> để dễ dàng phân tích.

Quy tắc quan trọng:
- LUÔN và CHỈ sử dụng công cụ getFaqsTool để tìm kiếm thông tin từ cơ sở tri thức trước khi trả lời
- KHÔNG bịa đặt thông tin - chỉ sử dụng dữ liệu từ công cụ getFaqsTool
- Nếu không tìm thấy thông tin, hãy lịch sự thông báo và gợi ý khách hàng diễn đạt lại câu hỏi
- Sử dụng tên khách hàng ${contactName ? contactName : ''} một cách tự nhiên khi có thể
- Nếu khách hàng cần hỗ trợ trực tiếp hoặc có vấn đề phức tạp, sử dụng collectLeadTool để thu thập thông tin liên hệ
- Nếu phát hiện spam hoặc ngôn từ không phù hợp, sử dụng humanHandoffTool để chuyển cho nhân viên
- Khi cần gửi hình ảnh theo danh sách URL, sử dụng công cụ sendImagesLinkTool
- Không được gửi url hình ảnh trực tiếp trong <reply> xml tag.
- Chủ động dùng tool sendImagesLinkTool để gửi hình ảnh cho khách hàng.
- Không được chào hỏi lại liên tục, phải tiếp nối cuộc trò chuyện tự nhiên nhất.
        `;
        break;
      default:
        agentName = "ecommerceAgent";
        break;
    }
  } else {
  }

  // Lấy agent từ mastra
  const agent = mastra.getAgent(agentName as "ecommerceAgent" | "ragAgent");

  // Tạo context cho agent nếu có thông tin chatbot
  let customInstructions = undefined;
  if (chatbotInfo?.instruction) {
    customInstructions = chatbotInfo.instruction;
  }

  type ChatbotRuntimeContext = {
    bot_id: string;
    tenant_id: string;
    thread_id: string;
    resource_id: string;
    account_id: string;
    conversation_id: string;
  };

  const runtimeContext = new RuntimeContext<ChatbotRuntimeContext>();
  runtimeContext.set("bot_id", botIdToUse);
  runtimeContext.set("tenant_id", tenantIdToUse);
  runtimeContext.set("thread_id", threadIdToUse);
  runtimeContext.set("resource_id", resourceIdToUse);
  runtimeContext.set("account_id", accoutnId || "");
  runtimeContext.set("conversation_id", conversationId || "");

  // Retry logic với exponential backoff
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
    const startTime = Date.now();

    try {

      // Chọn agent để sử dụng - nếu đã fail 2 lần, thử fallback với OpenAI
      let currentAgent = agent;

      // if (attempt >= 2) {
      //   try {
      //     const { Agent } = await import("@mastra/core/agent");
      //     const { openai } = await import("@ai-sdk/openai");

      //     currentAgent = new Agent({
      //       name: "Fallback E-commerce Agent",
      //       instructions: customInstructions || "You are a helpful e-commerce assistant.",
      //       model: openai("gpt-4o-mini"),
      //       tools: await agent.getTools({ runtimeContext }),
      //     }) as any; // Type assertion để tránh conflict
      //   } catch (fallbackError) {
      //     console.warn("⚠️ Could not create fallback agent, using original:", fallbackError);
      //     currentAgent = agent;
      //   }
      // }

      // Gọi agent để tạo phản hồi
      const response = await currentAgent.generate(
        message,
        {
          threadId: threadIdToUse,
          resourceId: resourceIdToUse,
          temperature: 0.5,
          maxSteps: 5,
          maxRetries: 2, // Built-in retry của Mastra
          telemetry: {
            isEnabled: true,
            metadata: telemetryMetadata,
          },
          // providerOptions: {
          //   google: {
          //     thinkingConfig: {
          //       thinkingBudget: 1000,
          //       includeThoughts: true
          //     },
          //   } satisfies GoogleGenerativeAIProviderOptions,
          // },
          // Thêm system message nếu có custom instructions
          instructions: `
${agentTypeInstruction}

### Custom Instructions and Context
${customInstructions || ''}

Current time: ${new Date().toLocaleString()}

${examplesTemplate}
          `,
          runtimeContext,
          onStepFinish: async (step) => {

            // Xử lý tool humanHandoffTool - chuyển giao sang nhân viên
            if (step.toolCalls.find((item) => item.toolName === "humanHandoffTool")) {
              try {
                await fetch(
                  `https://app.mooly.vn/api/v1/accounts/${accoutnId}/conversations/${conversationId}/toggle_status`,
                  {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                      api_access_token: apiToken,
                    },
                    body: JSON.stringify({
                      status: "open",
                    }),
                  }
                );
              } catch (error) {
                console.error("❌ Lỗi khi chuyển giao sang nhân viên:", error);
              }
            }

            // Xử lý tool detectSpamTool - phát hiện spam
            if (step.toolCalls.find((item) => item.toolName === "detectSpamTool")) {
              try {
                await fetch(
                  `https://app.mooly.vn/api/v1/accounts/${accoutnId}/conversations/${conversationId}/toggle_status`,
                  {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                      api_access_token: apiToken,
                    },
                    body: JSON.stringify({
                      status: "open",
                    }),
                  }
                );
              } catch (error) {
                console.error("❌ Lỗi khi xử lý spam:", error);
              }
            }

            // Xử lý tool collectLeadTool - lưu thông tin lead vào Supabase
            if (step.toolCalls.find((item) => item.toolName === "collectLeadTool")) {
              const contactTool = step.toolCalls.find((item) => item.toolName === "collectLeadTool");
              const contactResult = step.toolResults.find(result => result.toolName === "collectLeadTool");

              if (contactTool?.args?.phone && contactResult?.result?.success) {
                try {
                  // Lấy thông tin từ runtime context
                  const tenant_id = runtimeContext.get("tenant_id");
                  const bot_id = runtimeContext.get("bot_id");

                  // Sử dụng contact name từ webhook hoặc fallback
                  const customerName = contactName || 'Khách hàng';

                  // Tạo lead data
                  const leadData = {
                    tenant_id: tenant_id,
                    full_name: customerName,
                    chatbot_id: bot_id || null,
                    phone: contactTool.args.phone,
                    conversation_id: conversationId,
                    source: 'chatbot',
                    status: 'new',
                    lead_data: {
                      phone: contactTool.args.phone,
                      collected_at: new Date().toISOString(),
                      conversation_id: conversationId,
                      chatbot_type: chatbotInfo?.type || 'rag',
                      customer_name: customerName
                    },
                    notes: `Lead được thu thập từ ${chatbotInfo?.type || 'RAG'} chatbot qua conversation ${conversationId}`,
                    last_contact_at: new Date().toISOString(),
                    lead_score: 50 // Điểm mặc định cho lead từ chatbot
                  };

                  const { error } = await supabaseAdmin
                    .from('chatbot_leads')
                    .insert(leadData)
                    .select();

                  if (error) {
                    console.error("❌ Lỗi khi lưu lead vào Supabase:", error);
                  } else {
                  }
                } catch (error) {
                  console.error("❌ Lỗi khi xử lý lưu lead:", error);
                }
              }
            }

            // Helper function để gửi hình ảnh - tối ưu và tránh duplicate
            const sendImages = async (images: string[], source: string) => {
              if (!images || !Array.isArray(images) || images.length === 0) {
                console.log(`⚠️ ${source}: Không có hình ảnh hợp lệ để gửi`);
                return;
              }

              console.log(`📸 Đang gửi ${images.length} hình ảnh từ ${source}...`);

              try {
                await fetch(
                  `https://app.mooly.vn/api/v1/accounts/${accoutnId}/conversations/${conversationId}/messages`,
                  {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                      api_access_token: apiToken,
                    },
                    body: JSON.stringify({
                      content: "",
                      attachments: images.slice(0, 10).map((imageUrl: string) => ({
                        "file_type": "image",
                        "external_url": imageUrl
                      })),
                      "private": false
                    }),
                  }
                );
              } catch (error) {
                console.error(`❌ Lỗi khi gửi hình ảnh từ ${source}:`, error);
              }
            };

            // Kiểm tra và xử lý các tool gửi hình ảnh - logic tối ưu
            const imageTools = ["sendImagesTool", "sendImagesLinkTool"];
            const hasImageTool = step.toolCalls.find((item) => imageTools.includes(item.toolName));

            if (hasImageTool) {
              const toolResult = step.toolResults.find(result => imageTools.includes(result.toolName));

              if (toolResult) {
                // Xử lý sendImagesLinkTool - gửi trực tiếp danh sách URL hình ảnh
                if (toolResult.toolName === "sendImagesLinkTool") {
                  const images = toolResult?.args?.images || toolResult?.result?.images;
                  if (images && Array.isArray(images) && images.length > 0) {
                    await sendImages(images, toolResult.toolName);
                  }
                }

                // Xử lý sendImagesTool - lấy hình ảnh từ product_id
                else if (toolResult.toolName === "sendImagesTool" && toolResult?.result?.product_id) {
                  try {
                    const tenant_id = runtimeContext.get("tenant_id");
                    const productImagesResult = await getProductImages({
                      product_id: toolResult.result.product_id,
                      tenant_id: tenant_id.toString(),
                      maxImages: 10,
                    });

                    if (productImagesResult.success && productImagesResult.data?.images) {
                      const { images, product_name } = productImagesResult.data;
                      if (images && images.length > 0) {
                        console.log(`📸 Gửi ${images.length} hình ảnh của sản phẩm "${product_name}"`);
                        await sendImages(images, `${toolResult.toolName} (${product_name})`);
                      }
                    } else {
                      console.log(`⚠️ Không thể lấy hình ảnh sản phẩm: ${productImagesResult.message}`);
                    }
                  } catch (error) {
                    console.error(`❌ Lỗi khi xử lý ${toolResult.toolName} với product_id: ${toolResult?.result?.product_id}:`, error);
                  }
                }
              }
            }
            // Chỉ xử lý các tool tìm kiếm sản phẩm nếu KHÔNG có image tool
            else {
              // Xử lý tool searchProductsTool - gửi hình ảnh sản phẩm tìm được
              if (step.toolCalls.find((item) => item.toolName === "searchProductsTool")) {
                const toolResult = step.toolResults.find(result => result.toolName === "searchProductsTool");

                if (toolResult?.result?.products && Array.isArray(toolResult.result.products)) {
                  // Lấy hình ảnh từ các sản phẩm tìm được (tối đa 5 sản phẩm)
                  const productsToSend = toolResult.result.products.slice(0, 5);
                  const productImages = productsToSend
                    .map((product: any) => product?.avatar || product?.images?.[0])
                    .filter((img: string) => img && img.trim() !== '');

                  if (productImages.length > 0) {
                    await sendImages(productImages, "searchProductsTool");
                  }
                }
              }

              // Xử lý tool getProductDetailsTool - gửi tất cả hình ảnh của sản phẩm
              if (step.toolCalls.find((item) => item.toolName === "getProductDetailsTool")) {
                const toolResult = step.toolResults.find(result => result.toolName === "getProductDetailsTool");

                if (toolResult?.result?.product_details) {
                  const product = toolResult.result.product_details;

                  // Tạo danh sách hình ảnh từ sản phẩm
                  const productImages: string[] = [];

                  // Thêm avatar nếu có
                  if (product.avatar) {
                    productImages.push(product.avatar);
                  }

                  // Thêm các hình ảnh khác nếu có
                  if (product.images && Array.isArray(product.images)) {
                    productImages.push(...product.images.filter((img: string) => img && img !== product.avatar));
                  }

                  if (productImages.length > 0) {
                    await sendImages(productImages, `getProductDetailsTool (${product.name || 'Sản phẩm'})`);
                  }
                }
              }
            }
          },
        }
      );

      // Validate response sử dụng utility function
      const validatedResponse = validateAgentResponse(response);
      const duration = Date.now() - startTime;
      logAgentMetrics(attempt + 1, true, duration, validatedResponse.usage);

      return validatedResponse;

    } catch (error) {
      lastError = error as Error;
      const duration = Date.now() - startTime;

      logAgentMetrics(attempt + 1, false, duration, undefined, lastError);
      // console.error(`❌ Agent attempt ${attempt + 1} failed:`, error);

      // Kiểm tra xem lỗi có thể retry được không
      if (!isRetryableError(lastError)) {
        console.error(`💥 Non-retryable error encountered:`, lastError);
        break;
      }

      // Nếu đây là attempt cuối cùng, break
      if (attempt === retryConfig.maxRetries) {
        // console.error(`💥 All ${retryConfig.maxRetries + 1} attempts failed. Last error:`, lastError);
        break;
      }

      // Tính toán delay cho attempt tiếp theo với jitter
      const delay = calculateRetryDelay(attempt, retryConfig.baseDelay, retryConfig.maxDelay);
      await sleep(delay);
    }
  }

  // Nếu tất cả attempts đều fail, tạo fallback response
  console.error(`💥 Creating fallback response after ${retryConfig.maxRetries + 1} failed attempts`);
  const fallbackResponse = createFallbackResponse(lastError!);

  // Log fallback metrics
  logAgentMetrics(retryConfig.maxRetries + 1, false, 0, fallbackResponse.usage, lastError!);

  return fallbackResponse;
};
